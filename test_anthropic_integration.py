#!/usr/bin/env python3
"""
测试Anthropic API集成的脚本
用于验证新的Anthropic服务是否正常工作
"""

import asyncio
import os
import sys
from typing import List, Dict

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.services.anthropic_service import AnthropicService, get_anthropic_service
from app.services.llm_service import call_llm, is_anthropic_model
from app.core.config import settings


async def test_anthropic_service():
    """测试Anthropic服务基本功能"""
    print("=" * 60)
    print("测试Anthropic服务基本功能")
    print("=" * 60)
    
    # 检查API密钥
    api_key = settings.ANTHROPIC_API_KEY
    if not api_key:
        print("❌ 错误: 未设置ANTHROPIC_API_KEY环境变量")
        print("请在.env文件中设置ANTHROPIC_API_KEY")
        return False
    
    print(f"✅ API密钥已设置: {api_key[:10]}...")
    
    try:
        # 创建服务实例
        service = AnthropicService(api_key)
        print("✅ Anthropic服务实例创建成功")
        
        # 测试简单的非流式调用
        messages = [
            {"role": "user", "content": "请用中文回答：什么是人工智能？请简短回答，不超过100字。"}
        ]
        
        print("\n🔄 测试非流式调用...")
        response = await service.call_llm(
            messages=messages,
            flag="test_basic",
            model="claude-3-5-haiku-20241022",
            max_tokens=200
        )
        
        if response:
            print(f"✅ 非流式调用成功")
            print(f"📝 响应内容: {response[:100]}...")
        else:
            print("❌ 非流式调用失败")
            return False
            
        # 测试流式调用
        print("\n🔄 测试流式调用...")
        stream_content = ""
        chunk_count = 0
        
        async for chunk in service.call_llm_stream(
            messages=messages,
            flag="test_stream",
            model="claude-3-5-haiku-20241022",
            max_tokens=200
        ):
            stream_content += chunk
            chunk_count += 1
            if chunk_count <= 3:  # 只显示前3个块
                print(f"📦 流式块 {chunk_count}: {chunk[:50]}...")
        
        if stream_content:
            print(f"✅ 流式调用成功，共接收 {chunk_count} 个块")
            print(f"📝 完整内容: {stream_content[:100]}...")
        else:
            print("❌ 流式调用失败")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False


async def test_model_detection():
    """测试模型检测功能"""
    print("\n" + "=" * 60)
    print("测试模型检测功能")
    print("=" * 60)
    
    test_models = [
        ("claude-3-7-sonnet-20250219", True),
        ("claude-sonnet-4-20250514", True)
    ]
    
    for model, expected in test_models:
        result = is_anthropic_model(model)
        status = "✅" if result == expected else "❌"
        print(f"{status} {model}: {result} (期望: {expected})")
    
    return True


async def test_llm_service_integration():
    """测试LLM服务集成"""
    print("\n" + "=" * 60)
    print("测试LLM服务集成")
    print("=" * 60)
    
    # 检查API密钥
    api_key = settings.ANTHROPIC_API_KEY
    if not api_key:
        print("❌ 跳过集成测试: 未设置ANTHROPIC_API_KEY")
        return False
    
    try:
        # 测试通过统一接口调用Anthropic模型
        messages = [
            {"role": "user", "content": "请用中文简单介绍一下Claude AI，不超过50字。"}
        ]
        
        print("🔄 通过统一LLM接口调用Anthropic模型...")
        response = await call_llm(
            messages=messages,
            flag="test_integration",
            model="claude-3-5-haiku-20241022",
            apiKey=api_key,
            max_tokens=100
        )
        
        if response:
            print(f"✅ 统一接口调用成功")
            print(f"📝 响应内容: {response}")
        else:
            print("❌ 统一接口调用失败")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {str(e)}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始测试Anthropic API集成")
    print(f"📍 当前工作目录: {os.getcwd()}")
    
    # 运行所有测试
    tests = [
        ("模型检测功能", test_model_detection()),
        ("Anthropic服务基本功能", test_anthropic_service()),
        ("LLM服务集成", test_llm_service_integration()),
    ]
    
    results = []
    for test_name, test_coro in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            result = await test_coro
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 出现异常: {str(e)}")
            results.append((test_name, False))
    
    # 输出测试结果摘要
    print("\n" + "=" * 60)
    print("测试结果摘要")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！Anthropic API集成成功！")
        return True
    else:
        print("⚠️  部分测试失败，请检查配置和实现")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
