# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Development Commands

### Development Environment
```bash
# Start the API server in development mode (with auto-reload)
python run.py

# Start database only (development mode)
docker-compose -f docker-compose-localdb.yml up -d

# Start full application stack
docker-compose up -d
```

### Database Operations
```bash
# Initialize database (first time setup)
aerich init-db

# Generate migration after model changes
aerich migrate

# Apply migrations (testing/production)
aerich upgrade
```

### Build & Deployment
```bash
# Build Docker image
./build.sh <version> <environment>

# Deploy to environment
./deploy.sh <version> <environment>
```

### Testing
```bash
# Run tests (basic test files are in test/ directory)
python -m pytest test/
```

## Architecture Overview

### Core Structure
- **FastAPI + Tortoise ORM**: Async web framework with PostgreSQL database
- **Multi-tenant System**: Organizations → Users → Projects with quota management
- **LLM Integration**: Support for Anthropic Claude and OpenRouter API models
- **Research Pipeline**: Automated search → summarize → iterate → report generation workflow

### Key Application Layers

#### API Layer (`app/api/`)
- **routes/**: Define API endpoints only, delegate business logic to services
- **schemas/**: Pydantic models for request/response validation
- Uses dependency injection for authentication and authorization

#### Business Logic (`app/services/`)
- **llm_service.py**: LLM model abstraction with Anthropic and OpenRouter support
- **research_service.py**: Core research report generation pipeline
- **anthropic_service.py**: Direct Anthropic API integration
- **prompts.py**: Centralized prompt templates

#### Data Layer (`app/models/`)
- Tortoise ORM models following PostgreSQL naming conventions
- Base model includes standard fields: id, created_at, updated_at, is_deleted
- Foreign keys: `{table_name}_id` format
- All models include Chinese comments

#### Core Infrastructure (`app/core/`)
- **config.py**: Environment-based configuration with detailed LLM parameters
- **logging.py**: Advanced logging with hourly rotation and retention policies
- **security.py**: JWT authentication and API key management
- **redis_client.py**: Redis connection for SSO token management

### Research Report Generation Flow

1. **Project Setup**: User creates project with configuration and members
2. **Literature Search**: SerpAPI + Google Scholar integration for reference gathering
3. **Content Analysis**: Jina API for URL content extraction and summarization
4. **LLM Processing**: Multi-stage report generation with iteration and refinement
5. **Output Generation**: Structured reports with references and downloadable formats

### Database Schema Patterns

- **Multi-level Permissions**: super_admin → org_admin → normal_user
- **Quota Management**: Organization and user-level usage tracking
- **Project Hierarchy**: Projects → Leaders → Members → Reports
- **Audit Logging**: All LLM calls tracked in model_call_log table

## Development Guidelines

### Code Organization
- Follow the `.cursor/rules/idea-gen.mdc` specifications strictly
- API routes must only define endpoints, business logic goes in services
- All new models require database migrations via `aerich migrate`
- Update `directories.md` when adding new modules or major structural changes

### Database Development
- Use PostgreSQL naming: snake_case, plural table names
- All tables need Chinese comments via `COMMENT ON TABLE/COLUMN`
- Foreign keys follow `{table}_id` pattern
- Include soft delete fields: `is_deleted` (boolean) + `deleted_at` (timestamp)

### LLM Integration
- Use `llm_service.py` for model abstraction
- Anthropic models: Direct API calls via `anthropic_service.py`
- OpenRouter models: HTTP requests via `llm_service.py`
- All LLM calls must be logged for quota tracking

### Configuration Management
- Environment variables defined in `.env` with examples in `.env.example`
- Settings loaded via `app/core/config.py` with detailed parameter documentation
- Support for multiple environments (dev/test/prod) via deployment scripts

### Logging System
- Loguru-based logging with Spring Boot format
- Hourly log rotation with automatic compression
- 30-day retention policy (configurable)
- Structured logging for all critical operations

## Environment Setup

### Required Environment Variables
```bash
# Database
DATABASE_URL=postgres://postgres:123456@localhost:5432/hi_ideagen

# Authentication
SECRET_KEY=your-jwt-secret-key
DEFAULT_ADMIN_USERNAME=idea_admin
DEFAULT_ADMIN_PASSWORD=your-secure-password

# External APIs
ANTHROPIC_API_KEY=your-anthropic-key
SERPAPI_API_KEY=your-serpapi-key
JINA_API_KEY=your-jina-key

# Optional: Redis for SSO
REDIS_URL=redis://localhost:6379/0
IS_OPEN_SSO=OFF
```

### Local Development
1. Copy `.env.example` to `.env` and configure
2. Start PostgreSQL: `docker-compose -f docker-compose-localdb.yml up -d`
3. Install dependencies: `pip install -r requirements.txt`
4. Run application: `python run.py`
5. Access API docs: http://localhost:8000/docs

## Critical Implementation Notes

### Regex Performance Warning
- Test all regex patterns against `灾难性回溯文本.md` to prevent CPU spikes
- Use non-greedy quantifiers and proper escaping for complex text processing

### Migration Workflow
- Always generate migrations before code commits
- Use aerich pre-commit hook for automatic migration generation
- Apply migrations with `aerich upgrade` in staging/production environments

### Multi-Model LLM Support
- Check model type with `is_anthropic_model()` function
- Route Anthropic models to direct API, others to OpenRouter
- Implement proper error handling and retry logic for API calls