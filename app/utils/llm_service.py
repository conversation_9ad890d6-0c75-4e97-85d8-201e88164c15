import json
import aiohttp
import asyncio
import traceback
from typing import Dict, List, Optional, Callable
import os
from pydantic import UUID4
from app.core.config import settings
from app.core.logging import get_logger
from app.api.schemas.user import UserResponse
from app.utils.constants import ProductType
from app.api.repository.llm_call_log import create_llm_call_log, update_llm_call

# 获取logger实例
logger = get_logger(__name__)

# 导入Anthropic服务
try:
    from app.services.anthropic_service import call_anthropic_llm_stream
    from app.services.llm_service import is_anthropic_model
    ANTHROPIC_AVAILABLE = True
    logger.info("Utils中Anthropic服务模块加载成功")
except ImportError as e:
    ANTHROPIC_AVAILABLE = False
    logger.warning(f"Utils中Anthropic服务模块加载失败: {str(e)}")

async def stream_llm_and_save(
    messages: List[Dict[str, str]],
    apiKey,
    apiUrl,
    model,
    user: UserResponse,
    flag: str,
    callback: Callable[[str], None],
    complete_callback: Callable[[str], None],
    error_callback: Callable[[str], None],
    related_id: Optional[UUID4] = None,
    product_type = ProductType.DOCGEN.value
) -> str:
    """
    流式调用LLM，实时保存响应内容到文件，并返回完整响应
    
    Args:
        messages: 聊天消息列表
        model: 模型名称
        callback: 可选的回调函数，每接收到一个chunk就调用一次
        complete_callback: 可选的完成回调函数，生成完成时调用
    
    Returns:
        完整的模型响应文本
    """
    logger.info(f"流式调用LLM并保存到文件，模型: {model}")
    if not apiKey:
        err_msg = "未设置API_KEY环境变量"
        logger.error(err_msg)
        error_callback and await error_callback(err_msg)
        return ""

    # 检查是否为Anthropic模型
    if is_anthropic_model(model) and ANTHROPIC_AVAILABLE:
        logger.info(f"使用Anthropic流式API调用模型: {model}")
        try:
            full_response = ""
            generation_id = None

            # 创建LLM调用日志
            if user:
                generation_id = await create_llm_call_log(
                    user_id=user.id,
                    model=model,
                    flag=flag,
                    related_id=related_id,
                    product_type=product_type
                )

            # 使用Anthropic流式API
            async for text_chunk in call_anthropic_llm_stream(
                messages=messages,
                flag=flag,
                model=model,
                apiKey=apiKey,
                max_tokens=50000,
                user=user,
                callback=callback
            ):
                full_response += text_chunk

            # 调用完成回调
            if complete_callback:
                await complete_callback(generation_id, full_response)

            # 更新LLM调用日志
            if generation_id and user:
                await update_llm_call(
                    generation_id=generation_id,
                    response_content=full_response,
                    input_tokens=0,  # Anthropic会在服务中记录
                    output_tokens=0,
                    success=True
                )

            return full_response

        except Exception as e:
            error_msg = f"Anthropic流式API调用失败，尝试降级到OpenRouter: {str(e)}"
            logger.error(error_msg)
            if error_callback:
                await error_callback(error_msg)
            # 继续使用OpenRouter

    # 使用原有的OpenRouter流式逻辑
    logger.info(f"使用OpenRouter流式API调用模型: {model}")
    headers = {
        "Authorization": f"Bearer {apiKey}",
        "X-Title": "Hi-IdeaGen",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": model,
        "messages": messages,
        "stream": True,
        "temperature": settings.LLM_DEFAULT_TEMPERATURE,
        "top_k": settings.LLM_DEFAULT_TOP_K,
        "top_p": settings.LLM_DEFAULT_TOP_P
    }
    
    # 设置超时
    timeout = aiohttp.ClientTimeout(total=3600)  # 30分钟
    logger.debug(f"流式LLM请求，超时设置: {timeout.total}秒,温度: {settings.LLM_DEFAULT_TEMPERATURE},上采样top_k: {settings.LLM_DEFAULT_TOP_K},上采样top_p: {settings.LLM_DEFAULT_TOP_P}")
    
    try:
        llm_log = await create_llm_call_log(
            current_user=user,
            model_name=model,
            model_api_key=apiKey,
            model_api_url=apiUrl,
            response="",
            messages=messages,
            product_type=product_type,
            related_id=related_id,
            flag=flag
        )
    except Exception as e:
        logger.error(f"创建大模型调用日志失败：{str(e)}")
    try:
        full_response = ""
        async with aiohttp.ClientSession() as session:
            logger.debug(f"向OpenRouter发送流式请求: {settings.OPENROUTER_URL}")
            async with session.post(
                apiUrl,
                headers=headers,
                json=payload,
                timeout=timeout
            ) as resp:
                if resp.status != 200:
                    error_text = await resp.text()
                    error_msg = f"OpenRouter API 错误: 状态码 {resp.status}, 错误详情: {error_text}"
                    logger.error(error_msg)
                    error_callback and await error_callback(error_msg)
                    return f"API请求错误: {error_msg}"
                
                # 流式响应并写入文件
                logger.debug(f"开始接收流式LLM响应并写入文件")
                chunk_count = 0
                open_router_id: Optional[str] = None
                # 结束原因：
                stop_reason = {
                    "finish_reason": "",
                    "native_finish_reason": ""
                }
                # token消耗
                token_consumed = {
                    "input": "",
                    "output": ""
                }
                # 使用追加模式写入文件
                try:
                    async for line in resp.content:
                        try:
                            line_str = line.decode('utf-8').strip()
                            if not line_str:
                                continue
                            
                            # 跳过 "data: " 前缀
                            if line_str.startswith("data: "):
                                line_str = line_str[6:]
                            
                            # 处理流结束标记
                            if line_str == "[DONE]":
                                logger.debug("流式响应结束")
                                break
                            # 解析 JSON 数据
                            try:
                                data = json.loads(line_str)
                                open_router_id = data.get("id")
                                # print(open_router_id)
                                # 提取内容增量
                                if 'choices' in data and len(data['choices']) > 0:
                                    delta = data['choices'][0].get('delta', {})
                                    if 'content' in delta and delta['content']:
                                        chunk = delta['content']

                                        # is_not_empty = chunk.strip() and not chunk.isspace()
                                        # 只有当chunk包含非空白字符时，才将其拼接到full_response
                                        if chunk:
                                            full_response += chunk
                                        logger.info(f"LLM返回的文字：{chunk[:100]}")
                                        # 如果有回调函数，则调用
                                        if callback and chunk:
                                            await callback(chunk)
                                        
                                        chunk_count += 1
                                    finish_reason = data['choices'][0].get('finish_reason', "")
                                    native_finish_reason = data['choices'][0].get('native_finish_reason', "")
                                    if finish_reason:
                                        stop_reason["finish_reason"] = finish_reason
                                    if native_finish_reason:
                                        stop_reason["native_finish_reason"] = native_finish_reason
                                if 'usage' in data and data["usage"]:
                                    token_consumed["input"] = data["usage"]["prompt_tokens"]
                                    token_consumed["output"] = data["usage"]["completion_tokens"]
                            except json.JSONDecodeError as je:
                                logger.error(f"无法解析 JSON '{line_str[:100]}...': {str(je)}")
                            except Exception as e:
                                logger.error(f"处理响应数据时出错: {str(e)}, 数据: {line_str[:100]}...")
                        except UnicodeDecodeError as ue:
                            logger.error(f"解码响应时出错: {str(ue)}")
                        except Exception as e:
                            logger.error(f"处理响应行时出错: {str(e)}")
                            
                except Exception as e:
                    logger.error(f"读取响应流时出错: {str(e)}")
                
                logger.info(f"流式响应完成，接收了 {chunk_count} 个数据块，总长度: {len(full_response)}")
                
                # 如果没有收到任何内容，记录更多的调试信息
                if not full_response:
                    result = f"没有接收到有效内容"
                    logger.warning(result)
                    if error_callback:
                        await error_callback(result)
                    # 写入一些信息到文件，表明请求成功但没有收到内容
                    return f"请求完成，但未收到有效内容"
                
                # 调用完成回调
                if complete_callback:
                    try:
                        logger.info("LLM流式已经完成了。现在调用complete_callback函数")
                        logger.info(f"报告{related_id}的结束原因：{str(stop_reason)},token消耗：{str(token_consumed)}")
                        await complete_callback(open_router_id, full_response)
                        await update_llm_call(
                            log_id=llm_log.id,
                            response=full_response
                        )
                    except Exception as e:
                        logger.error(f"执行完成回调时出错: {str(e)}")
                
                return full_response
                    
    except asyncio.TimeoutError as te:
        error_msg = f"OpenRouter API 请求超时 ({timeout.total} 秒)"
        logger.error(error_msg)
        return f"请求超时: {error_msg}"
    except aiohttp.ClientError as ce:
        error_msg = f"HTTP客户端错误: {str(ce)}"
        logger.error(error_msg)
        return error_msg
    except Exception as e:
        error_msg = f"调用OpenRouter时发生错误: {str(e)}"
        logger.error(error_msg)
        logger.debug(f"错误详情:\n{traceback.format_exc()}")
        return error_msg 
    except asyncio.CancelledError:
        logger.warning("请求被取消")
        return "请求被取消"