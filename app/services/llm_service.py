import json
import aiohttp
import asyncio
import traceback
from typing import Dict, List, Optional, AsyncGenerator, Any, Callable
import os
from app.api.schemas.user import UserResponse
from app.core.config import settings
from app.core.logging import get_logger

# 获取logger实例
logger = get_logger(__name__)

# 导入Anthropic服务
try:
    from app.services.anthropic_service import call_anthropic_llm, call_anthropic_llm_stream
    ANTHROPIC_AVAILABLE = True
    logger.info("Anthropic服务模块加载成功")
except ImportError as e:
    ANTHROPIC_AVAILABLE = False
    logger.warning(f"Anthropic服务模块加载失败: {str(e)}")

def is_anthropic_model(model: str) -> bool:
    """
    判断是否为Anthropic模型

    Args:
        model: 模型名称

    Returns:
        bool: 是否为Anthropic模型
    """
    anthropic_models = [
        "claude-sonnet-4-20250514",
        "claude-3-7-sonnet-20250219"
    ]

    # 检查完整模型名或包含claude的模型名
    return model in anthropic_models or "claude" in model.lower()


def clean_json_format(content: str) -> str:
    """
    清洗JSON格式内容，移除markdown代码块等格式

    Args:
        content: 原始内容

    Returns:
        str: 清洗后的JSON字符串
    """
    import re

    if not content:
        return ""

    # 移除markdown JSON代码块格式
    content = re.sub(r'```json\s*\n(.*?)\n```', r'\1', content, flags=re.DOTALL)

    # 移除普通markdown代码块格式
    content = re.sub(r'```\s*\n(.*?)\n```', r'\1', content, flags=re.DOTALL)

    # 移除LaTeX格式
    content = re.sub(r'\\boxed\{(.*?)\}', r'\1', content, flags=re.DOTALL)

    # 移除多余的空白字符
    content = content.strip()

    return content


async def call_llm(
    messages: List[Dict[str, str]],
    flag: str = "default", # 调用的目的，用于区分不同的调用场景
    model: str = "",
    stream: bool = False,
    apiKey: str = "",
    apiUrl: str = "",
    max_tokens: Optional[int] = None,
    user: Optional[UserResponse] = None
) -> Optional[str]:
    """
    调用LLM API（支持OpenRouter和Anthropic）

    Args:
        messages: 聊天消息列表
        model: 模型名称
        stream: 是否流式响应
        apiKey: API密钥
        apiUrl: API地址
        max_tokens: 最大token数
        user: 用户信息

    Returns:
        模型响应文本，如果失败则返回None
    """
    logger.info(f"调用LLM，模型: {model}, 目的: {flag}, 流式: {stream}")

    # 检查是否为Anthropic模型
    if is_anthropic_model(model) and ANTHROPIC_AVAILABLE:
        logger.info(f"使用Anthropic API调用模型: {model}")
        try:
            # 使用Anthropic API
            return await call_anthropic_llm(
                messages=messages,
                flag=flag,
                model=model,
                stream=False,  # 此函数只处理非流式
                apiKey=apiKey,
                max_tokens=max_tokens or 50000,
                user=user
            )
        except Exception as e:
            logger.error(f"Anthropic API调用失败，尝试降级到OpenRouter: {str(e)}")
            # 降级到OpenRouter

    # 使用原有的OpenRouter逻辑
    logger.info(f"使用OpenRouter API调用模型: {model}")
    if not apiKey:
        error = "未设置API_KEY环境变量"
        logger.error(error)
        raise ValueError(error)
        
    headers = {
        "Authorization": f"Bearer {apiKey}",
        "X-Title": "Hi-IdeaGen",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": model,
        "messages": messages,
        "stream": stream
    }
    if max_tokens:
        payload["max_tokens"] = max_tokens
    
    # 设置超时
    timeout = aiohttp.ClientTimeout(total=3600)  # 30分钟
    logger.debug(f"LLM请求，超时设置: {timeout.total}秒，消息数量: {len(messages)}")
    
    try:
        async with aiohttp.ClientSession() as session:
            logger.debug(f"向OpenRouter发送请求: {apiUrl}")
            async with session.post(
                apiUrl,
                headers=headers,
                json=payload,
                timeout=timeout
            ) as resp:
                if resp.status != 200:
                    error_text = await resp.text()
                    logger.error(f"OpenRouter API 错误: 状态码 {resp.status}")
                    logger.error(f"错误详情: {error_text}")
                    return None
                
                # 处理非流式响应
                if not stream:
                    logger.debug("接收非流式LLM响应")
                    result = await resp.json()
                    logger.debug(f"完整响应: {result}")
                    
                    try:
                        # 检查OpenRouter返回的标准响应格式
                        if 'choices' in result and len(result['choices']) > 0:
                            if 'message' in result['choices'][0] and 'content' in result['choices'][0]['message']:
                                content = result['choices'][0]['message']['content']
                                logger.info(f"LLM响应成功(标准格式)，内容长度: {len(content)}")
                                return content
                        
                        # 检查替代响应格式: Claude格式
                        if 'content' in result:
                            content = result['content']
                            logger.info(f"LLM响应成功(Claude格式)，内容长度: {len(content)}")
                            return content
                            
                        # 检查替代响应格式: Gemini格式
                        if 'candidates' in result and len(result['candidates']) > 0:
                            if 'content' in result['candidates'][0] and 'parts' in result['candidates'][0]['content']:
                                text_parts = [part.get('text', '') for part in result['candidates'][0]['content']['parts'] if 'text' in part]
                                content = ''.join(text_parts)
                                logger.info(f"LLM响应成功(Gemini格式)，内容长度: {len(content)}")
                                return content
                        
                        # 检查替代响应格式: 纯文本
                        if isinstance(result, str):
                            logger.info(f"LLM响应成功(纯文本格式)，内容长度: {len(result)}")
                            return result
                            
                        # 如果都无法解析，尝试将整个响应转换为字符串
                        logger.warning(f"无法解析的响应格式，尝试转换为字符串: {result}")
                        
                        # 检查是否为Google AI的位置限制错误
                        if isinstance(result, dict) and 'error' in result:
                            error_data = result['error']
                            # 检查是否包含provider_name和Google位置限制信息
                            if 'metadata' in error_data and 'provider_name' in error_data['metadata']:
                                provider = error_data['metadata'].get('provider_name', '')
                                raw_error = error_data['metadata'].get('raw', '')
                                
                                if provider == 'Google AI Studio' and 'User location is not supported' in raw_error:
                                    logger.error(f"Google AI Studio 位置限制错误: {raw_error}")
                                    return "由于地理位置限制，无法访问Google AI服务。正在尝试其他方法解析内容..."
                            raise ValueError(result)
                        return json.dumps(result)
                            
                    except (KeyError, IndexError) as e:
                        logger.error(f"OpenRouter 响应结构异常: {str(e)}")
                        logger.debug(f"完整响应: {result}")
                        
                        # 尝试找到任何可能的文本内容
                        if isinstance(result, dict):
                            # 递归搜索字典中的任何文本内容
                            def extract_text(obj):
                                if isinstance(obj, str):
                                    return obj
                                elif isinstance(obj, dict):
                                    for k, v in obj.items():
                                        if k in ['content', 'text', 'message', 'response']:
                                            text = extract_text(v)
                                            if text:
                                                return text
                                    for v in obj.values():
                                        text = extract_text(v)
                                        if text:
                                            return text
                                elif isinstance(obj, list):
                                    for item in obj:
                                        text = extract_text(item)
                                        if text:
                                            return text
                                return None
                            
                            extracted_text = extract_text(result)
                            if extracted_text:
                                logger.info(f"从异常响应中提取到文本，长度: {len(extracted_text)}")
                                return extracted_text
                        
                        # 如果实在无法提取，返回整个响应的字符串形式
                        logger.warning("无法从响应中提取文本，返回整个响应的字符串形式")
                        return str(result)
                
                # 流式响应，返回完整内容
                else:
                    logger.debug("开始接收流式LLM响应")
                    full_response = ""
                    chunk_count = 0
                    async for line in resp.content:
                        line = line.decode('utf-8').strip()
                        if not line:
                            continue
                        
                        # 跳过 "data: " 前缀
                        if line.startswith("data: "):
                            line = line[6:]
                        
                        # 处理流结束标记
                        if line == "[DONE]":
                            logger.debug("流式响应结束")
                            break
                        
                        try:
                            # 解析 JSON 数据
                            data = json.loads(line)
                            logger.debug(f"流式响应数据: {data}")
                            
                            # 提取内容增量 - 标准格式
                            if 'choices' in data and len(data['choices']) > 0:
                                delta = data['choices'][0].get('delta', {})
                                if 'content' in delta:
                                    chunk = delta['content']
                                    full_response += chunk
                                    chunk_count += 1
                            # Claude格式
                            elif 'content' in data:
                                chunk = data['content']
                                full_response += chunk
                                chunk_count += 1
                            # Gemini格式
                            elif 'candidates' in data and len(data['candidates']) > 0:
                                if 'content' in data['candidates'][0] and 'parts' in data['candidates'][0]['content']:
                                    text_parts = [part.get('text', '') for part in data['candidates'][0]['content']['parts'] if 'text' in part]
                                    chunk = ''.join(text_parts)
                                    full_response += chunk
                                    chunk_count += 1
                            # 任何可能包含文本的字段
                            else:
                                for key in ['text', 'message', 'response', 'output']:
                                    if key in data:
                                        chunk = data[key] if isinstance(data[key], str) else str(data[key])
                                        full_response += chunk
                                        chunk_count += 1
                                        break
                        except json.JSONDecodeError:
                            logger.error(f"无法解析 JSON: {line}")
                        except Exception as e:
                            logger.error(f"处理流式响应时出错: {str(e)}")
                    
                    logger.info(f"流式响应完成，接收了 {chunk_count} 个数据块，总长度: {len(full_response)}")
                    return full_response
                    
    except asyncio.TimeoutError:
        logger.error(f"OpenRouter API 请求超时 ({timeout.total} 秒)")
        return None
    except aiohttp.ClientError as e:
        logger.error(f"aiohttp 客户端错误: {e.__class__.__name__}: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"调用 OpenRouter 时发生错误: {e.__class__.__name__}: {str(e)}")
        logger.debug(f"错误详情:\n{traceback.format_exc()}")
        return None


async def stream_llm_and_save(
    messages: List[Dict[str, str]],
    model: str,
    apiKey: str,
    apiUrl: str = "",
    user: Optional[UserResponse] = None,
    flag: str = "default",
    callback: Optional[Callable[[str], None]] = None,
    error_callback: Optional[Callable[[str], None]] = None,
    max_tokens: Optional[int] = None
) -> None:
    """
    流式调用LLM并通过回调处理响应（支持Anthropic和OpenRouter）

    Args:
        messages: 聊天消息列表
        model: 模型名称
        apiKey: API密钥
        apiUrl: API地址
        user: 用户信息
        flag: 调用目的标识
        callback: 内容回调函数
        error_callback: 错误回调函数
        max_tokens: 最大token数
    """
    logger.info(f"开始流式调用LLM，模型: {model}, 目的: {flag}")

    try:
        # 检查是否为Anthropic模型
        if is_anthropic_model(model) and ANTHROPIC_AVAILABLE:
            logger.info(f"使用Anthropic流式API调用模型: {model}")
            try:
                async for text_chunk in call_anthropic_llm_stream(
                    messages=messages,
                    flag=flag,
                    model=model,
                    apiKey=apiKey,
                    max_tokens=max_tokens or 50000,
                    user=user,
                    callback=callback
                ):
                    # 文本块已经通过callback处理，这里不需要额外处理
                    pass
                return
            except Exception as e:
                logger.error(f"Anthropic流式API调用失败，尝试降级到OpenRouter: {str(e)}")
                if error_callback:
                    error_callback(f"Anthropic API调用失败，降级到OpenRouter: {str(e)}")
                # 继续使用OpenRouter

        # 使用原有的OpenRouter流式逻辑
        logger.info(f"使用OpenRouter流式API调用模型: {model}")

        if not apiKey:
            error_msg = "未设置API_KEY环境变量"
            logger.error(error_msg)
            if error_callback:
                error_callback(error_msg)
            return

        headers = {
            "Authorization": f"Bearer {apiKey}",
            "X-Title": "Hi-IdeaGen",
            "Content-Type": "application/json"
        }

        payload = {
            "model": model,
            "messages": messages,
            "stream": True
        }
        if max_tokens:
            payload["max_tokens"] = max_tokens

        # 设置超时
        timeout = aiohttp.ClientTimeout(total=3600)  # 1小时

        async with aiohttp.ClientSession() as session:
            async with session.post(
                apiUrl or settings.OPENROUTER_URL,
                headers=headers,
                json=payload,
                timeout=timeout
            ) as resp:
                if resp.status != 200:
                    error_text = await resp.text()
                    error_msg = f"OpenRouter API 错误: 状态码 {resp.status}, 详情: {error_text}"
                    logger.error(error_msg)
                    if error_callback:
                        error_callback(error_msg)
                    return

                # 处理流式响应
                async for line in resp.content:
                    line = line.decode('utf-8').strip()
                    if not line:
                        continue

                    # 跳过 "data: " 前缀
                    if line.startswith("data: "):
                        line = line[6:]

                    # 处理流结束标记
                    if line == "[DONE]":
                        break

                    try:
                        # 解析 JSON 数据
                        data = json.loads(line)

                        # 提取内容增量
                        chunk = ""
                        if 'choices' in data and len(data['choices']) > 0:
                            delta = data['choices'][0].get('delta', {})
                            if 'content' in delta:
                                chunk = delta['content']

                        if chunk and callback:
                            callback(chunk)

                    except json.JSONDecodeError:
                        logger.debug(f"跳过非JSON行: {line}")
                    except Exception as e:
                        logger.error(f"处理流式响应时出错: {str(e)}")

    except Exception as e:
        error_msg = f"流式调用LLM失败: {str(e)}"
        logger.error(error_msg)
        if error_callback:
            error_callback(error_msg)


async def call_llm_with_format_json(
    messages: List[Dict[str, str]],
    api_key: str,
    api_url: str,
    model: str,
    response_format: Optional[Dict[str, Any]] = None,
    timeout: int = 300,
    # 调用场景的文字描述
    flag: str = "",
    user: Optional[UserResponse] = None
) -> str:
    """
    调用大模型并支持自定义 response_format，专门用于处理需要返回JSON格式的场景
    
    该方法会：
    1. 支持自定义 response_format 参数，用于指定返回格式
    2. 自动清洗返回内容中的JSON相关格式，包括：
       - 移除 markdown 代码块格式 (```json ... ```)
       - 移除 LaTeX 格式 (\boxed{ ... })
       - 移除普通 markdown 代码块 (``` ... ```)
    3. 返回清洗后的纯JSON字符串
    
    Args:
        messages: 消息列表
        api_key: API密钥
        api_url: API接口地址
        model: 模型名称
        response_format: 响应格式配置（可选），用于指定返回格式
        timeout: 超时时间（秒）
        
    Returns:
        str: 清洗后的JSON字符串，如果调用失败则返回空字符串
    """
    try:
        logger.info(f"开始调用大模型，model: {model}")

        # 检查是否为Anthropic模型
        if is_anthropic_model(model) and ANTHROPIC_AVAILABLE:
            logger.info(f"使用Anthropic API调用模型: {model}")
            try:
                # 使用Anthropic API
                result = await call_anthropic_llm(
                    messages=messages,
                    flag=flag,
                    model=model,
                    apiKey=api_key,
                    max_tokens=50000,
                    user=user
                )

                if result:
                    # 清洗JSON格式
                    cleaned_result = clean_json_format(result)
                    return cleaned_result
                else:
                    return ""

            except Exception as e:
                logger.error(f"Anthropic API调用失败，尝试降级到OpenRouter: {str(e)}")
                # 继续使用OpenRouter

        # 使用原有的OpenRouter逻辑
        logger.info(f"使用OpenRouter API调用模型: {model}")

        # 构建请求参数
        headers = {
            "Authorization": f"Bearer {api_key}",
            "X-Title": "Hi-IdeaGen",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": model,
            "messages": messages,
            "stream": False
        }
       
        # 如果提供了 response_format，添加到 payload 中
        if response_format:
            payload["response_format"] = response_format
        logger.info(f"请求参数: {json.dumps(payload, ensure_ascii=False)}")
        logger.info(f"请求参数: {payload}")
        # 设置超时
        timeout_obj = aiohttp.ClientTimeout(total=timeout)
        
        # 发送请求
        async with aiohttp.ClientSession() as session:
            async with session.post(
                api_url,
                headers=headers,
                json=payload,
                timeout=timeout_obj
            ) as resp:
                if resp.status != 200:
                    error_text = await resp.text()
                    logger.error(f"调用大模型失败: {error_text}")
                    return ""
                
                result = await resp.json()
                # 提取内容
                if 'choices' in result and len(result['choices']) > 0:
                    content = result['choices'][0].get('message', {}).get('content', '')
                    if content:
                        # 处理不同模型返回的格式
                        # 1. 处理 markdown 代码块格式 (```json ... ```)
                        if '```json' in content:
                            logger.info("'```json 处理")
                            content = content.split('```json')[1].split('```')[0].strip()
                        # 2. 处理 LaTeX 格式 (\boxed{ ... })
                        elif '\\boxed{' in content:
                            logger.info("'\\boxed 处理")
                            content = content.split('\\boxed{')[1]
                            last_brace = content.rfind('}')
                            if last_brace != -1:
                                content = content[:last_brace].strip()
                        # 3. 处理普通 markdown 代码块 (``` ... ```)
                        elif '```' in content:
                            logger.info("'```' 处理")
                            content = content.split('```')[1].split('```')[0].strip()
                        
                        return content
                
                logger.error("调用大模型失败")
                return ""
                
    except Exception as e:
        logger.error(f"调用大模型时发生错误: {str(e)}")
        return "" 