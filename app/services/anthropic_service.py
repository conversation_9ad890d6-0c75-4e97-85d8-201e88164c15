import asyncio
import json
from typing import Dict, List, Optional, AsyncGenerator, Any, Callable
from anthropic import AsyncAnthropic
from anthropic.types import MessageParam
from app.api.schemas.user import UserResponse
from app.core.config import settings
from app.core.logging import get_logger

# 获取logger实例
logger = get_logger(__name__)


class AnthropicService:
    """Anthropic Claude API服务类"""
    
    def __init__(self, api_key: str = None):
        """
        初始化Anthropic服务
        
        Args:
            api_key: Anthropic API密钥，如果不提供则从配置中获取
        """
        self.api_key = api_key or settings.ANTHROPIC_API_KEY
        if not self.api_key:
            raise ValueError("未设置ANTHROPIC_API_KEY环境变量")
        
        self.client = AsyncAnthropic(api_key=self.api_key)
    
    def _convert_messages_to_anthropic_format(self, messages: List[Dict[str, str]]) -> tuple[str, List[MessageParam]]:
        """
        将OpenAI格式的消息转换为Anthropic格式
        
        Args:
            messages: OpenAI格式的消息列表
            
        Returns:
            tuple: (system_prompt, anthropic_messages)
        """
        system_prompt = ""
        anthropic_messages = []
        
        for msg in messages:
            role = msg.get("role", "")
            content = msg.get("content", "")
            
            if role == "system":
                # Anthropic将system消息单独处理
                system_prompt = content
            elif role in ["user", "assistant"]:
                anthropic_messages.append({
                    "role": role,
                    "content": content
                })
        
        return system_prompt, anthropic_messages
    
    async def call_llm(
        self,
        messages: List[Dict[str, str]], 
        flag: str = "default",
        model: str = "claude-3-5-sonnet-20241022",
        stream: bool = False,
        max_tokens: int = 50000,
        temperature: float = 1.0,
        user: Optional[UserResponse] = None
    ) -> Optional[str]:
        """
        调用Anthropic Claude API（非流式）
        
        Args:
            messages: 聊天消息列表
            flag: 调用目的标识
            model: 模型名称
            stream: 是否流式响应（此方法固定为False）
            max_tokens: 最大token数
            temperature: 温度参数
            user: 用户信息
            
        Returns:
            模型响应文本，如果失败则返回None
        """
        logger.info(f"调用Anthropic LLM，模型: {model}, 目的: {flag}, max_tokens: {max_tokens}")
        
        try:
            # 转换消息格式
            system_prompt, anthropic_messages = self._convert_messages_to_anthropic_format(messages)
            
            # 构建请求参数
            request_params = {
                "model": model,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "messages": anthropic_messages
            }
            
            # 如果有system prompt，添加到请求中
            if system_prompt:
                request_params["system"] = system_prompt
            
            logger.debug(f"Anthropic API请求参数: {json.dumps(request_params, ensure_ascii=False, indent=2)}")
            
            # 调用API
            response = await self.client.messages.create(**request_params)
            
            # 提取响应内容
            if response.content and len(response.content) > 0:
                content = response.content[0].text
                logger.info(f"Anthropic API响应成功，内容长度: {len(content)}")
                
                # 记录使用统计
                if hasattr(response, 'usage'):
                    logger.info(f"Token使用统计 - 输入: {response.usage.input_tokens}, 输出: {response.usage.output_tokens}")
                
                return content
            else:
                logger.error("Anthropic API返回空内容")
                return None
                
        except Exception as e:
            logger.error(f"调用Anthropic API失败: {str(e)}")
            return None
    
    async def call_llm_stream(
        self,
        messages: List[Dict[str, str]], 
        flag: str = "default",
        model: str = "claude-3-5-sonnet-20241022",
        max_tokens: int = 50000,
        temperature: float = 1.0,
        user: Optional[UserResponse] = None,
        callback: Optional[Callable[[str], None]] = None
    ) -> AsyncGenerator[str, None]:
        """
        调用Anthropic Claude API（流式）
        
        Args:
            messages: 聊天消息列表
            flag: 调用目的标识
            model: 模型名称
            max_tokens: 最大token数
            temperature: 温度参数
            user: 用户信息
            callback: 回调函数，用于处理每个流式响应片段
            
        Yields:
            流式响应的文本片段
        """
        logger.info(f"调用Anthropic LLM流式接口，模型: {model}, 目的: {flag}, max_tokens: {max_tokens}")
        
        try:
            # 转换消息格式
            system_prompt, anthropic_messages = self._convert_messages_to_anthropic_format(messages)
            
            # 构建请求参数
            request_params = {
                "model": model,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "messages": anthropic_messages,
                "stream": True
            }
            
            # 如果有system prompt，添加到请求中
            if system_prompt:
                request_params["system"] = system_prompt
            
            logger.debug(f"Anthropic API流式请求参数: {json.dumps({k: v for k, v in request_params.items() if k != 'stream'}, ensure_ascii=False, indent=2)}")
            
            # 调用流式API
            async with self.client.messages.stream(**request_params) as stream:
                async for text in stream.text_stream:
                    if text:
                        # 如果有回调函数，调用它
                        if callback:
                            try:
                                callback(text)
                            except Exception as callback_error:
                                logger.error(f"回调函数执行失败: {str(callback_error)}")
                        
                        yield text
                        
                # 获取最终的消息和使用统计
                final_message = await stream.get_final_message()
                if hasattr(final_message, 'usage'):
                    logger.info(f"流式Token使用统计 - 输入: {final_message.usage.input_tokens}, 输出: {final_message.usage.output_tokens}")
                        
        except Exception as e:
            logger.error(f"调用Anthropic流式API失败: {str(e)}")
            # 在流式调用失败时，yield错误信息
            yield f"[错误] Anthropic API调用失败: {str(e)}"


# 全局Anthropic服务实例
_anthropic_service = None

def get_anthropic_service(api_key: str = None) -> AnthropicService:
    """
    获取Anthropic服务实例（单例模式）
    
    Args:
        api_key: API密钥，如果不提供则使用默认配置
        
    Returns:
        AnthropicService实例
    """
    global _anthropic_service
    
    if _anthropic_service is None or (api_key and api_key != _anthropic_service.api_key):
        _anthropic_service = AnthropicService(api_key)
    
    return _anthropic_service


# 兼容性函数，保持与原有代码的接口一致
async def call_anthropic_llm(
    messages: List[Dict[str, str]], 
    flag: str = "default",
    model: str = "claude-3-5-sonnet-20241022",
    stream: bool = False,
    apiKey: str = "",
    apiUrl: str = "",  # Anthropic不需要自定义URL，保留参数以兼容
    max_tokens: Optional[int] = None,
    user: Optional[UserResponse] = None
) -> Optional[str]:
    """
    兼容性函数：调用Anthropic Claude API
    
    这个函数保持与原有call_llm函数相同的接口，方便替换现有代码
    """
    try:
        service = get_anthropic_service(apiKey if apiKey else None)
        
        return await service.call_llm(
            messages=messages,
            flag=flag,
            model=model,
            stream=False,  # 此函数只处理非流式
            max_tokens=max_tokens or 50000,
            user=user
        )
    except Exception as e:
        logger.error(f"兼容性函数调用失败: {str(e)}")
        return None


async def call_anthropic_llm_stream(
    messages: List[Dict[str, str]], 
    flag: str = "default",
    model: str = "claude-3-5-sonnet-20241022",
    apiKey: str = "",
    apiUrl: str = "",  # Anthropic不需要自定义URL，保留参数以兼容
    max_tokens: Optional[int] = None,
    user: Optional[UserResponse] = None,
    callback: Optional[Callable[[str], None]] = None
) -> AsyncGenerator[str, None]:
    """
    兼容性函数：调用Anthropic Claude API（流式）
    
    这个函数保持与原有流式调用相似的接口，方便替换现有代码
    """
    try:
        service = get_anthropic_service(apiKey if apiKey else None)
        
        async for text in service.call_llm_stream(
            messages=messages,
            flag=flag,
            model=model,
            max_tokens=max_tokens or 50000,
            user=user,
            callback=callback
        ):
            yield text
    except Exception as e:
        logger.error(f"兼容性流式函数调用失败: {str(e)}")
        yield f"[错误] 流式调用失败: {str(e)}"
