from typing import Optional, Union, Dict, Any
from uuid import UUID
import json
import re

from app.api.repository.user_default_model import get_user_model
from app.models.insight.inspirations_canvas_report_relation import InspirationSourceType
from app.api.schemas.user import UserResponse
from app.models.insight.inspirations import Inspiration
from app.models.insight.knowledge_canvas import KnowledgeCanvas
from app.models.model_config import ModelConfig
from app.models.organization_model_use import UseCase
from app.models.user import User
from app.services.llm_service import call_llm, call_llm_with_format_json
from app.services.insight.insight_prompts import (
    generate_analysis_prompt, 
    insight_generate_outline_prompt, 
    generate_key_points_prompt, 
    generate_probe_questions_prompt, 
    generate_probe_answer_prompt
)
from app.services.insight.knowledge_canvas_service import generate_summary
from app.core.logging import get_logger
from app.utils.utils import send_data

logger = get_logger(__name__)


def clean_outline_format(outline_content: str) -> list:
    """
    清洗大纲格式，提取层级嵌套的标题结构
    
    Args:
        outline_content: AI生成的大纲内容
    
    Returns:
        清洗后的层级标题结构列表，格式：
        [
            {
                "title": "一级标题",
                "subtitle": [
                    {
                        "title": "二级标题", 
                        "subtitle": [
                            {
                                "title": "三级标题",
                                "content": "内容文本"
                            }
                        ],
                        "content": None
                    }
                ],
                "content": None
            }
        ]
    """
    
    def clean_markdown_format(text: str) -> str:
        """
        清理文本中的所有Markdown格式标记 - 性能优化版本
        
        Args:
            text: 原始文本
        
        Returns:
            清理后的文本
        """
        if not text:
            return text
        
        # 1. 去除标题格式 (####, ###, ##, #)
        text = re.sub(r'^#{1,6}\s+', '', text, flags=re.MULTILINE)
        
        # 2. 去除粗体格式 (**text** 或 __text__) - 简化版，避免回溯
        text = re.sub(r'\*\*([^\*\n]{1,200}?)\*\*', r'\1', text)
        text = re.sub(r'__([^_\n]{1,200}?)__', r'\1', text)
        
        # 3. 去除斜体格式 (*text* 或 _text_) - 简化版，移除复杂断言
        text = re.sub(r'\*([^\*\n]{1,200}?)\*', r'\1', text)
        text = re.sub(r'_([^_\n]{1,200}?)_', r'\1', text)
        
        # 4. 去除删除线格式 (~~text~~) - 简化版
        text = re.sub(r'~~([^~\n]{1,200}?)~~', r'\1', text)
        
        # 5. 去除行内代码格式 (`text`) - 简化版，避免跨行匹配
        text = re.sub(r'`([^`\n]{1,200}?)`', r'\1', text)
        
        # 6. 去除代码块格式 (```language 或 ```) - 简化版
        text = re.sub(r'^```[a-zA-Z]*\s*$', '', text, flags=re.MULTILINE)
        text = re.sub(r'^```\s*$', '', text, flags=re.MULTILINE)
        
        # 7. 去除链接格式 [text](url) 或 [text][ref] - 简化版，限制长度
        text = re.sub(r'\[([^\]]{1,100}?)\]\([^)]{1,200}?\)', r'\1', text)
        text = re.sub(r'\[([^\]]{1,100}?)\]\[[^\]]{0,50}?\]', r'\1', text)
        
        # 8. 去除图片格式 ![alt](url) - 简化版
        text = re.sub(r'!\[([^\]]{0,100}?)\]\([^)]{1,200}?\)', r'\1', text)
        
        # 9. 去除引用格式 (> text)
        text = re.sub(r'^>\s+', '', text, flags=re.MULTILINE)
        
        # 10. 去除列表标记 (- item, * item, + item, 1. item)
        text = re.sub(r'^[-*+]\s+', '', text, flags=re.MULTILINE)
        text = re.sub(r'^\d+\.\s+', '', text, flags=re.MULTILINE)
        
        # 11. 去除水平分割线 (--- 或 ***) - 简化版
        text = re.sub(r'^[-*]{3,}\s*$', '', text, flags=re.MULTILINE)
        
        # 12. 去除表格分隔符 (|)
        text = re.sub(r'\|', ' ', text)
        
        # 13. 去除多余的空行和空格
        text = re.sub(r'\n\s*\n', '\n', text)
        text = re.sub(r'\s+', ' ', text)
        
        return text.strip()
    
    try:
        # ✅ 输入验证和长度限制，防止超大文本导致内存溢出
        if not outline_content or not outline_content.strip():
            return []
        
        # 限制总输入文本长度 (20000字)
        MAX_INPUT_LENGTH = 20000  # 20000字
        if len(outline_content) > MAX_INPUT_LENGTH:
            logger.warning(f"输入文本过长 ({len(outline_content)} 字符)，截断到 {MAX_INPUT_LENGTH} 字符")
            outline_content = outline_content[:MAX_INPUT_LENGTH]
        
        result = []
        lines = outline_content.strip().split('\n')
        
        current_h1 = None
        current_h2 = None
        current_h3 = None
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            
            # 匹配一级标题 (# 标题)
            h1_match = re.match(r'^#\s+(.+)$', line)
            if h1_match:
                current_h1 = {
                    "title": h1_match.group(1).strip(),
                    "subtitle": [],
                    "content": None
                }
                result.append(current_h1)
                current_h2 = None
                current_h3 = None
                continue
            
            # 匹配二级标题 (## 标题)
            h2_match = re.match(r'^##\s+(.+)$', line)
            if h2_match:
                if current_h1 is None:
                    # 如果没有一级标题，创建一个默认的一级标题
                    current_h1 = {
                        "title": "默认标题",
                        "subtitle": [],
                        "content": None
                    }
                    result.append(current_h1)
                
                current_h2 = {
                    "title": h2_match.group(1).strip(),
                    "subtitle": [],
                    "content": None
                }
                current_h1["subtitle"].append(current_h2)
                current_h3 = None
                continue
            
            # 匹配三级标题 (### 标题)
            h3_match = re.match(r'^###\s+(.+)$', line)
            if h3_match:
                if current_h2 is None:
                    # 如果没有二级标题，创建默认结构
                    if current_h1 is None:
                        current_h1 = {
                            "title": "默认标题",
                            "subtitle": [],
                            "content": None
                        }
                        result.append(current_h1)
                    
                    current_h2 = {
                        "title": "默认子标题",
                        "subtitle": [],
                        "content": None
                    }
                    current_h1["subtitle"].append(current_h2)
                
                current_h3 = {
                    "title": h3_match.group(1).strip(),
                    "subtitle": [],
                    "content": None
                }
                current_h2["subtitle"].append(current_h3)
                continue
            
            # 匹配四级及以上标题 (#### 标题或更多#)，将其作为内容处理
            h4_or_more_match = re.match(r'^#{4,}\s+(.+)$', line)
            if h4_or_more_match:
                # 将四级及以上标题当作内容处理，但去掉 Markdown 格式
                content_text = clean_markdown_format(line)
                
                # 确定内容应该归属到哪个层级
                if current_h3:
                    if current_h3["content"]:
                        current_h3["content"] += f"\n{content_text}"
                    else:
                        current_h3["content"] = content_text
                elif current_h2:
                    if current_h2["content"]:
                        current_h2["content"] += f"\n{content_text}"
                    else:
                        current_h2["content"] = content_text
                elif current_h1:
                    if current_h1["content"]:
                        current_h1["content"] += f"\n{content_text}"
                    else:
                        current_h1["content"] = content_text
                continue
            
            # 匹配内容项 (数字. 内容)
            content_match = re.match(r'^\d+\.\s+(.+)$', line)
            if content_match:
                content_text = clean_markdown_format(content_match.group(1).strip())
                
                # 确定内容应该归属到哪个层级
                if current_h3:
                    # 三级标题下的内容
                    if current_h3["content"]:
                        current_h3["content"] += f"\n{content_text}"
                    else:
                        current_h3["content"] = content_text
                elif current_h2:
                    # 二级标题下的内容
                    if current_h2["content"]:
                        current_h2["content"] += f"\n{content_text}"
                    else:
                        current_h2["content"] = content_text
                elif current_h1:
                    # 一级标题下的内容
                    if current_h1["content"]:
                        current_h1["content"] += f"\n{content_text}"
                    else:
                        current_h1["content"] = content_text
                continue
            
            # 其他文本内容
            if line:
                # 清理 Markdown 格式
                cleaned_line = clean_markdown_format(line)
                
                # 确定内容应该归属到哪个层级
                if current_h3:
                    if current_h3["content"]:
                        current_h3["content"] += f"\n{cleaned_line}"
                    else:
                        current_h3["content"] = cleaned_line
                elif current_h2:
                    if current_h2["content"]:
                        current_h2["content"] += f"\n{cleaned_line}"
                    else:
                        current_h2["content"] = cleaned_line
                elif current_h1:
                    if current_h1["content"]:
                        current_h1["content"] += f"\n{cleaned_line}"
                    else:
                        current_h1["content"] = cleaned_line
        
        # 清理结果：如果有子标题列表，则content设为None
        def clean_node(node):
            if node.get("subtitle") and len(node["subtitle"]) > 0:
                node["content"] = None
                # 递归清理子节点
                for sub_node in node["subtitle"]:
                    clean_node(sub_node)
            elif not node.get("subtitle"):
                node["subtitle"] = []
            
            # 对最终的 content 再次清理 Markdown 格式
            if node.get("content"):
                node["content"] = clean_markdown_format(node["content"])
        
        for item in result:
            clean_node(item)
        
        # 额外清洗：当一级标题只有一个时，提升层级
        if len(result) == 1 and result[0].get("subtitle"):
            logger.info("检测到只有一个一级标题，进行层级提升清洗")
            single_h1 = result[0]
            
            # 将二级标题提升为一级标题，三级标题提升为二级标题
            promoted_result = []
            for h2_item in single_h1["subtitle"]:
                promoted_h1 = {
                    "title": h2_item["title"],
                    "subtitle": [],
                    "content": h2_item["content"]
                }
                
                # 将原来的三级标题作为新的二级标题
                if h2_item.get("subtitle"):
                    for h3_item in h2_item["subtitle"]:
                        promoted_h2 = {
                            "title": h3_item["title"],
                            "subtitle": [],  # 三级变二级后，不再有子标题
                            "content": h3_item["content"]
                        }
                        promoted_h1["subtitle"].append(promoted_h2)
                
                # 重新应用清理规则
                clean_node(promoted_h1)
                promoted_result.append(promoted_h1)
            
            result = promoted_result
            logger.info(f"层级提升完成，现在有 {len(result)} 个一级标题项目")
        
        logger.info(f"大纲清洗完成，提取了 {len(result)} 个一级标题项目")
        return result
        
    except Exception as e:
        logger.error(f"清洗大纲格式失败: {str(e)}")
        return []


# 定义 JSON Schema
KEYNOTES_SCHEMA = {
    "type": "array",
    "items": {
        "type": "object",
        "properties": {
            "keynote_title": {
                "type": "string",
                "description": "重点标题"
            },
            "keynote_sub_title": {
                "type": "array",
                "description": "重点子标题列表",
                "items": {
                    "type": "string"
                }
            }
        },
        "required": ["keynote_title", "keynote_sub_title"],
        "additionalProperties": False
    }
}

PROBE_SCHEMA = {
    "type": "array",
    "items": {
        "type": "string",
        "description": "追问问题"
    }
}

async def get_content_by_id_and_type(content_id: UUID, content_type: str) -> Optional[Union[Inspiration, KnowledgeCanvas]]:
    """
    根据内容ID和类型获取对应的内容对象
    
    Args:
        content_id: 内容ID
        content_type: 内容类型 (inspiration 或 knowledge_canvas)
    
    Returns:
        对应的内容对象或None
    """
    try:
        if content_type == InspirationSourceType.INSPIRATION.value:
            return await Inspiration.get_or_none(id=content_id, is_deleted=False)
        elif content_type == InspirationSourceType.CANVAS.value:
            return await KnowledgeCanvas.get_or_none(id=content_id, is_deleted=False)
        else:
            logger.error(f"不支持的内容类型: {content_type}")
            return None
    except Exception as e:
        logger.error(f"获取内容失败: {str(e)}")
        return None


def get_content_for_analysis(content_obj: Union[Inspiration, KnowledgeCanvas], content_type: str) -> tuple[str, str]:
    """
    根据内容类型获取用于分析的内容和名称
    
    Args:
        content_obj: 内容对象
        content_type: 内容类型
    
    Returns:
        (名称, 内容) 元组
    """
    if content_type == InspirationSourceType.INSPIRATION.value:
        name = content_obj.name or "未命名灵感库"
        content = content_obj.summary or content_obj.content
    elif content_type == InspirationSourceType.CANVAS.value:  # knowledge_canvas
        name = content_obj.name or "未命名知识卡片"
        content = content_obj.original_article  or content_obj.original_article_truncated
    
    return name, content

def get_content_for_keynotes(content_obj: Union[Inspiration, KnowledgeCanvas], content_type: str) -> str:
    """
    根据内容类型获取用于生成重点的内容
    
    Args:
        content_obj: 内容对象
        content_type: 内容类型
    
    Returns:
        用于生成重点的内容字符串
    """
    if content_type == InspirationSourceType.INSPIRATION:
        return f"灵感库名称：{content_obj.name}\n灵感库内容：{content_obj.content}\n灵感库简介：{content_obj.summary}\n灵感库来源：{content_obj.source}\n灵感库综述：{content_obj.original_article_truncated}"
    elif content_type == InspirationSourceType.CANVAS:  # knowledge_canvas
        return content_obj.original_article or content_obj.original_article_truncated

def get_content_for_prompts(content_obj: Union[Inspiration, KnowledgeCanvas], content_type: str) -> str:
    """
    根据内容类型获取用于生成提示词的内容
    
    Args:
        content_obj: 内容对象
        content_type: 内容类型
    
    Returns:
        用于生成提示词的内容字符串
    """
    if content_type == InspirationSourceType.INSPIRATION:
        return content_obj.content or content_obj.summary
    elif content_type == InspirationSourceType.CANVAS:  # knowledge_canvas
        return content_obj.original_article or content_obj.original_article_truncated

async def save_analysis_result(content_obj: Union[Inspiration, KnowledgeCanvas], analysis_content: str):
    """
    保存分析结果到对应的内容对象
    
    Args:
        content_obj: 内容对象
        analysis_content: 分析内容
    """
    try:
        content_obj.ai_analysis = analysis_content
        await content_obj.save()
    except Exception as e:
        logger.error(f"保存分析结果失败: {str(e)}")
        raise

async def save_outline_result(content_obj: Union[Inspiration, KnowledgeCanvas], outline_content: str):
    """
    保存大纲结果到对应的内容对象
    
    Args:
        content_obj: 内容对象
        outline_content: 大纲内容
    """
    try:
        content_obj.ai_outline = outline_content
        await content_obj.save()
    except Exception as e:
        logger.error(f"保存大纲结果失败: {str(e)}")
        raise

async def save_keynotes_result(content_obj: Union[Inspiration, KnowledgeCanvas], keynotes_content: str):
    """
    保存重点结果到对应的内容对象
    
    Args:
        content_obj: 内容对象
        keynotes_content: 重点内容
    """
    try:
        content_obj.ai_keynotes = keynotes_content
        await content_obj.save()
    except Exception as e:
        logger.error(f"保存重点结果失败: {str(e)}")
        raise

async def save_probe_result(content_obj: Union[Inspiration, KnowledgeCanvas], probe_content: str):
    """
    保存追问结果到对应的内容对象
    
    Args:
        content_obj: 内容对象
        probe_content: 追问内容
    """
    try:
        content_obj.ai_probe = probe_content
        await content_obj.save()
    except Exception as e:
        logger.error(f"保存追问结果失败: {str(e)}")
        raise

async def save_summary_result(content_obj: Union[Inspiration, KnowledgeCanvas], summary_content: str):
    """
    保存概要结果到对应的内容对象
    
    Args:
        content_obj: 内容对象
        summary_content: 概要内容
    """
    try:
        content_obj.summary = summary_content
        await content_obj.save()
    except Exception as e:
        logger.error(f"保存概要结果失败: {str(e)}")
        raise

async def ai_analyze_content(
    content_id: UUID,
    content_type: str,
    current_user: UserResponse
) -> Dict[str, Any]:
    """
    AI分析内容的通用方法
    
    Args:
        content_id: 内容ID
        content_type: 内容类型 (INSPIRATION 或 CANVAS)
        current_user: 用户
    
    Returns:
        分析结果字典
    """
    try:
        # 获取内容对象 knowledge_canvas 或 inspiration
        content_obj = await get_content_by_id_and_type(content_id, content_type)
        if not content_obj:
            return send_data(False, None, f"未找到对应的{('灵感库' if content_type == InspirationSourceType.INSPIRATION else '知识卡片')}")
        
        # 获取分析内容
        name, content = get_content_for_analysis(content_obj, content_type)
        if not content:
            return send_data(False, None, f"{'内容' if content_type == InspirationSourceType.INSPIRATION else '原始文章内容'}为空")
        
        # 获取模型配置
        model_config = await get_user_model(current_user, UseCase.INSIGHT_MIND_MAP.value)
        
        if not model_config:
            logger.warning(f"用户ID {current_user.id} 没有配置LLM模型")
            return send_data(False, None, "用户没有配置LLM模型")
        
        # 生成分析提示词
        messages = generate_analysis_prompt(name=name, content=content)
        if not messages:
            return send_data(False, None, "生成分析提示词失败")
        
        # 调用LLM服务进行分析
        analysis_content = await call_llm(
            messages=messages,
            flag=f"analyze_{content_type}_{content_id}",
            model=model_config.model_name,
            apiKey=model_config.api_key,
            apiUrl=model_config.api_url
        )
        
        if not analysis_content:
            return send_data(False, None, "AI生成分析内容为空")
        
        # 保存分析结果
        await save_analysis_result(content_obj, analysis_content)
        
        logger.info(f"成功为{content_type} {content_id} 生成AI分析")
        return send_data(True, {
            "analysis": analysis_content
        })
        
    except Exception as e:
        logger.error(f"AI分析失败: {str(e)}")
        return send_data(False, None, f"AI分析失败: {str(e)}")

async def ai_generate_outline(
    content_id: UUID,
    content_type: str,
    current_user: UserResponse
) -> Dict[str, Any]:
    """
    AI生成大纲的通用方法
    
    Args:
        content_id: 内容ID
        content_type: 内容类型 (inspiration 或 knowledge_canvas)
        current_user: 用户
    
    Returns:
        大纲结果字典
    """
    try:
        # 获取内容对象
        content_obj = await get_content_by_id_and_type(content_id, content_type)
        if not content_obj:
            return send_data(False, None, f"未找到对应的{('灵感库' if content_type == InspirationSourceType.INSPIRATION else '知识卡片')}")
        
    
        
        # 获取模型配置
        model_config = await get_user_model(current_user, UseCase.INSIGHT_MIND_MAP.value)
        if not model_config:
            logger.warning(f"用户ID {current_user.id} 没有配置LLM模型")
            return send_data(False, None, "用户没有配置LLM模型")
        
        # 生成概要
        logger.info(f"为{content_type} {content_id} 生成概要")
        content_for_summary = get_content_for_prompts(content_obj, content_type)
        summary = await generate_summary(content_for_summary, content_id, model_config)
        if summary:
            await save_summary_result(content_obj, summary)
        
        # 生成大纲（只有当ai_outline为空时才生成）
        logger.info(f"为{content_type} {content_id} 生成大纲")
        content_for_outline = get_content_for_prompts(content_obj, content_type)
        # 生成提示词
        messages = insight_generate_outline_prompt(name=content_obj.name, content=content_for_outline)
        if not messages:
            return send_data(False, None, "生成提示词失败")
        
        # 调用LLM服务
        outline_content = await call_llm(
            messages=messages,
            flag=f"generate_outline_{content_id}",
            model=model_config.model_name,
            apiKey=model_config.api_key,
            apiUrl=model_config.api_url
        )
        
        logger.info(f"AI生成大纲内容: {outline_content}")
        if not outline_content:
            return send_data(False, None, "AI生成大纲内容为空")
        
        # 清洗大纲格式
        cleaned_outline = clean_outline_format(outline_content)
        logger.info(f"清洗后的大纲结构: {cleaned_outline}")
        
        # 保存大纲结果
        await save_outline_result(content_obj, outline_content)
        
        # 返回概要、原始大纲和清洗后的结构化大纲
        return send_data(True, {
            "summary": summary,
            "outline": outline_content,
            "cleaned_outline": cleaned_outline,
            "combined": f"{summary or ''} {outline_content or ''}".strip()
        })
        
    except Exception as e:
        logger.error(f"AI生成大纲失败: {str(e)}")
        return send_data(False, None, f"AI生成大纲失败: {str(e)}")

async def ai_generate_keynotes(
    content_id: UUID,
    content_type: str,
    current_user: UserResponse
) -> Dict[str, Any]:
    """
    AI生成重点的通用方法
    
    Args:
        content_id: 内容ID
        content_type: 内容类型 (inspiration 或 knowledge_canvas)
        current_user: 用户
    
    Returns:
        重点结果字典
    """
    try:
        # 获取内容对象
        content_obj = await get_content_by_id_and_type(content_id, content_type)
        if not content_obj:
            return send_data(False, None, f"未找到对应的{('灵感库' if content_type == InspirationSourceType.INSPIRATION else '知识卡片')}")
        
        # 获取模型配置
        model_config = await get_user_model(current_user, UseCase.INSIGHT_MIND_MAP.value)
        if not model_config:
            logger.warning(f"用户ID {current_user.id} 没有配置LLM模型")
            return send_data(False, None, "用户没有配置LLM模型")
        
        # 获取用于生成重点的内容
        content_for_keynotes = get_content_for_keynotes(content_obj, content_type)
        
        # 生成提示词
        messages = generate_key_points_prompt(content=content_for_keynotes)
        if not messages:
            return send_data(False, None, "生成提示词失败")
        
        # 定义 response_format
        response_format = {
            "type": "json_schema",
            "json_schema": {
                "name": "keynotes_result",
                "strict": True,
                "schema": KEYNOTES_SCHEMA
            }
        }
        
        # 调用LLM服务
        keynotes_content = await call_llm_with_format_json(
            messages=messages,
            model=model_config.model_name,
            api_key=model_config.api_key,
            api_url=model_config.api_url,
            response_format=response_format
        )
        logger.info(f"AI生成重点内容: {keynotes_content}")
        if not keynotes_content:
            return send_data(False, None, "AI生成重点内容为空")
        
        try:
            # 解析JSON内容
            keynotes_data = json.loads(keynotes_content)
            
            # 保存重点结果
            await save_keynotes_result(content_obj, keynotes_content)
            
            return send_data(True, {
                "keynotes": keynotes_data
            })
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {str(e)}")
            return send_data(False, None, "AI生成的重点内容格式错误")
        
    except Exception as e:
        logger.error(f"AI生成重点失败: {str(e)}")
        return send_data(False, None, f"AI生成重点失败: {str(e)}")

async def ai_generate_probe_questions(
    content_id: UUID,
    content_type: str,
    current_user: UserResponse
) -> Dict[str, Any]:
    """
    AI生成追问问题的通用方法
    
    Args:
        content_id: 内容ID
        content_type: 内容类型 (inspiration 或 knowledge_canvas)
        current_user: 用户
    
    Returns:
        追问问题结果字典
    """
    try:
        # 获取内容对象
        content_obj = await get_content_by_id_and_type(content_id, content_type)
        if not content_obj:
            return send_data(False, None, f"未找到对应的{('灵感库' if content_type == InspirationSourceType.INSPIRATION else '知识卡片')}")
        
        # 获取模型配置
        model_config = await get_user_model(current_user, UseCase.INSIGHT_MIND_MAP.value)
        if not model_config:
            logger.warning(f"用户ID {current_user.id} 没有配置LLM模型")
            return send_data(False, None, "用户没有配置LLM模型")
        
        # 获取用于生成追问的内容
        content_for_probe = get_content_for_prompts(content_obj, content_type)
        logger.info(f"{content_type}.content: {content_for_probe}")
        
        # 生成提示词
        messages = generate_probe_questions_prompt(content=content_for_probe)
        if not messages:
            return send_data(False, None, "生成提示词失败")
        
        # 定义 response_format
        response_format = {
            "type": "json_schema",
            "json_schema": {
                "name": "probe_result",
                "strict": True,
                "schema": PROBE_SCHEMA
            }
        }
        
        # 调用LLM服务
        probe_content = await call_llm_with_format_json(
            messages=messages,
            model=model_config.model_name,
            api_key=model_config.api_key,
            api_url=model_config.api_url,
            response_format=response_format
        )
        
        logger.info(f"AI生成追问内容: {probe_content}")
        if not probe_content:
            return send_data(False, None, "AI生成追问内容为空")
        
        try:
            # 解析JSON内容
            probe_data = json.loads(probe_content)
            
            # 保存追问结果
            await save_probe_result(content_obj, probe_content)
            
            return send_data(True, {
                "probes": probe_data
            })
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {str(e)}")
            return send_data(False, None, "AI生成的追问内容格式错误")
        
    except Exception as e:
        logger.error(f"AI生成追问失败: {str(e)}")
        return send_data(False, None, f"AI生成追问失败: {str(e)}")

async def ai_generate_probe_answer(
    content_id: UUID,
    content_type: str,
    current_user: UserResponse,
    question: str
) -> Dict[str, Any]:
    """
    AI生成追问回答的通用方法
    
    Args:
        content_id: 内容ID
        content_type: 内容类型 (inspiration 或 knowledge_canvas)
        current_user: 用户
        question: 需要回答的问题
    
    Returns:
        追问回答结果字典
    """
    try:
        # 获取内容对象
        content_obj = await get_content_by_id_and_type(content_id, content_type)
        if not content_obj:
            return send_data(False, None, f"未找到对应的{('灵感库' if content_type == InspirationSourceType.INSPIRATION else '知识卡片')}")
        
        # 获取模型配置
        model_config = await get_user_model(current_user, UseCase.INSIGHT_MIND_MAP.value)
        if not model_config:
            logger.warning(f"用户ID {current_user.id} 没有配置LLM模型")
            return send_data(False, None, "用户没有配置LLM模型")
        
        # 获取用于生成回答的内容
        content_for_answer = get_content_for_prompts(content_obj, content_type)
        logger.info(f"AI生成回答内容: {content_for_answer}")
        # 生成提示词
        messages = generate_probe_answer_prompt(content=content_for_answer, question=question)
        if not messages:
            return send_data(False, None, "生成提示词失败")
        logger.info(f"AI生成回答提示词: {messages}")
        # 调用LLM服务
        answer_content = await call_llm(
            messages=messages,
            flag=f"probe_answer_{content_id}",
            model=model_config.model_name,
            apiKey=model_config.api_key,
            apiUrl=model_config.api_url
        )
        
        if not answer_content:
            return send_data(False, None, "AI生成回答内容为空")
        
        return send_data(True, {
            "answer": answer_content
        })
        
    except Exception as e:
        logger.error(f"AI生成回答失败: {str(e)}")
        return send_data(False, None, f"AI生成回答失败: {str(e)}")