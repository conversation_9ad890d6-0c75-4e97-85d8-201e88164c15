from pydantic import BaseModel, UUID4, ConfigDict
from typing import Optional
from datetime import datetime, timezone


class ModelConfigBase(BaseModel):
    """模型配置基础模型"""
    model_name: str
    name: Optional[str] = None
    api_key: str
    api_url: str
    max_context: int = 0
    max_output: int = 0
    description: Optional[str] = None
    is_active: bool = False
    id: UUID4


class ModelConfigCreate(BaseModel):
    """创建模型配置的请求模型"""
    model_name: str
    name: Optional[str] = None
    api_key: str
    api_url: str
    max_context: int = 0
    max_output: int = 0
    description: Optional[str] = None
    is_active: Optional[bool] = True


class ModelConfigUpdate(BaseModel):
    """更新模型配置的请求模型"""
    name: Optional[str] = None
    model_name: Optional[str] = None
    api_key: Optional[str] = None
    api_url: Optional[str] = None
    max_context: Optional[int] = None
    max_output: Optional[int] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None


class ModelConfigInDB(ModelConfigBase):
    """数据库中的模型配置模型"""
    id: UUID4
    user_id: UUID4
    created_at: datetime
    updated_at: Optional[datetime] = None
    is_deleted: bool = False

    class Config:
        # orm_mode = True
        from_attributes = True
    # model_config = ConfigDict(from_attributes=True)


class ModelConfigResponse(ModelConfigInDB):
    """API响应模型"""
    pass


class ModelConfigList(BaseModel):
    """分页查询响应模型"""
    total: int
    items: list[ModelConfigResponse] 