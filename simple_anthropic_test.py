#!/usr/bin/env python3
"""
简化的Anthropic API集成测试脚本
用于快速验证Anthropic服务是否正常工作
"""

import asyncio
import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.services.llm_service import is_anthropic_model, call_llm
from app.core.config import settings


async def test_model_detection():
    """测试模型检测功能"""
    print("🧪 测试模型检测功能")
    print("-" * 40)
    
    test_cases = [
        ("claude-3-5-sonnet-20241022", True),
        ("claude-3-5-haiku-20241022", True),
        ("gpt-4", False),
        ("anthropic/claude-3.5-sonnet", True),
    ]
    
    all_passed = True
    for model, expected in test_cases:
        result = is_anthropic_model(model)
        status = "✅" if result == expected else "❌"
        print(f"{status} {model}: {result}")
        if result != expected:
            all_passed = False
    
    return all_passed


async def test_basic_call():
    """测试基本的LLM调用"""
    print("\n🧪 测试基本LLM调用")
    print("-" * 40)
    
    # 检查API密钥
    api_key = settings.ANTHROPIC_API_KEY
    if not api_key:
        print("⚠️  跳过API调用测试: 未设置ANTHROPIC_API_KEY")
        print("请在.env文件中设置ANTHROPIC_API_KEY以进行完整测试")
        return True
    
    try:
        messages = [
            {"role": "user", "content": "请用中文简单回答：什么是人工智能？限制在50字以内。"}
        ]
        
        print("🔄 调用Anthropic API...")
        response = await call_llm(
            messages=messages,
            flag="simple_test",
            model="claude-3-5-haiku-20241022",
            apiKey=api_key,
            max_tokens=100
        )
        
        if response:
            print("✅ API调用成功")
            print(f"📝 响应: {response}")
            return True
        else:
            print("❌ API调用失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False


async def main():
    """主测试函数"""
    print("🚀 Anthropic API集成简化测试")
    print("=" * 50)
    
    # 运行测试
    tests = [
        ("模型检测", test_model_detection()),
        ("基本API调用", test_basic_call()),
    ]
    
    results = []
    for test_name, test_coro in tests:
        try:
            result = await test_coro
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 异常: {str(e)}")
            results.append((test_name, False))
    
    # 输出结果
    print("\n" + "=" * 50)
    print("📊 测试结果")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 基本功能测试通过！")
    else:
        print("⚠️  部分测试失败，请检查配置")
    
    return passed == len(results)


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
